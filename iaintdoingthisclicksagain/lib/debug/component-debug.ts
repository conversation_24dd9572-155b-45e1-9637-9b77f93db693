import { useEffect, useRef } from 'react';
import { logger } from './logger';

// Store for component debug info
const componentDebugStore: Record<string, any> = {};

export function useComponentDebug(componentName: string, props: any = {}): void {
  // Disable component debugging for better performance
  if (process.env.NODE_ENV !== 'development') return;

  const renderCount = useRef(0);
  const mountTime = useRef(Date.now());

  // Simplified tracking with reduced overhead
  useEffect(() => {
    renderCount.current += 1;

    // Only store essential component info
    componentDebugStore[componentName] = {
      name: componentName,
      renderCount: renderCount.current,
      mountTime: mountTime.current,
      lastRenderTime: Date.now(),
    };

    // Cleanup on unmount
    return () => {
      // Mark as unmounted but keep in store for debugging
      if (componentDebugStore[componentName]) {
        componentDebugStore[componentName].unmounted = true;
        componentDebugStore[componentName].unmountTime = Date.now();
      }
    };
  }, [componentName]); // Removed props dependency to reduce re-renders
}

export function getComponentDebugInfo(): Record<string, any> {
  return { ...componentDebugStore };
}

export function clearComponentDebugInfo(): void {
  Object.keys(componentDebugStore).forEach(key => {
    delete componentDebugStore[key];
  });
}