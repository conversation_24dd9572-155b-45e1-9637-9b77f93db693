#!/usr/bin/env node

/**
 * Bundle Analysis Script
 * 
 * This script helps analyze the Next.js bundle to identify performance bottlenecks
 * and optimization opportunities.
 */

const { execSync } = require('child_process');
const fs = require('fs');
const path = require('path');

console.log('🔍 Starting bundle analysis...\n');

// Check if @next/bundle-analyzer is installed
try {
  require.resolve('@next/bundle-analyzer');
} catch (error) {
  console.log('📦 Installing @next/bundle-analyzer...');
  execSync('bun add -D @next/bundle-analyzer', { stdio: 'inherit' });
}

// Create a temporary next.config.js with bundle analyzer
const originalConfig = fs.readFileSync('next.config.mjs', 'utf8');
const analyzerConfig = `
import bundleAnalyzer from '@next/bundle-analyzer';

const withBundleAnalyzer = bundleAnalyzer({
  enabled: process.env.ANALYZE === 'true',
});

${originalConfig.replace('export default nextConfig', 'export default withBundleAnalyzer(nextConfig)')}
`;

// Write temporary config
fs.writeFileSync('next.config.analyzer.mjs', analyzerConfig);

try {
  console.log('🏗️  Building with bundle analysis...');
  
  // Build with analysis
  execSync('ANALYZE=true bun run build', { 
    stdio: 'inherit',
    env: { ...process.env, NEXT_CONFIG_FILE: 'next.config.analyzer.mjs' }
  });
  
  console.log('\n✅ Bundle analysis complete!');
  console.log('📊 Check the opened browser tabs for detailed bundle analysis.');
  
} catch (error) {
  console.error('❌ Bundle analysis failed:', error.message);
} finally {
  // Clean up temporary config
  if (fs.existsSync('next.config.analyzer.mjs')) {
    fs.unlinkSync('next.config.analyzer.mjs');
  }
}

// Additional performance tips
console.log('\n💡 Performance Optimization Tips:');
console.log('1. Look for large chunks in the bundle analyzer');
console.log('2. Consider code splitting for large components');
console.log('3. Use dynamic imports for heavy libraries');
console.log('4. Optimize images and use Next.js Image component');
console.log('5. Remove unused dependencies');
console.log('6. Use tree shaking for libraries that support it');
